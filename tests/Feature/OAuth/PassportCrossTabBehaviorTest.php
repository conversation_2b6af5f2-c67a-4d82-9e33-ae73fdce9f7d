<?php

namespace Tests\Feature\OAuth;

use App\Models\Passport\Client;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;

class PassportCrossTabBehaviorTest extends TestCase
{
    use RefreshDatabase;

    protected Client $client;

    #[\Override]
    protected function setUp(): void
    {
        parent::setUp();

        // Create a test OAuth client
        $this->client = Client::create([
            'id' => 'test-crosstab-client',
            'name' => 'GravityWriteDefaultRedirect',
            'secret' => 'test-secret',
            'redirect' => 'http://localhost/test/callback',
            'personal_access_client' => false,
            'password_client' => false,
            'revoked' => false,
        ]);
    }

    /**
     * Test cross-tab scenario: User opens OAuth link in Tab 1, then opens different OAuth link in Tab 2
     */
    public function test_cross_tab_oauth_request_override(): void
    {
        $this->assertDatabaseIsolation();

        // Simulate Tab 1: User clicks OAuth link for login
        $tab1OAuthUrl = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
        ]);

        // Simulate OAuth redirect to login page (Tab 1)
        Session::put('redirect_uri', $tab1OAuthUrl);
        Session::put('is_first_time', true);

        // Verify Tab 1 state
        $response1 = $this->get(route('login'));
        $response1->assertStatus(200);
        $response1->assertViewIs('auth.login');

        // Verify is_first_time was processed
        $this->assertFalse(Session::get('is_first_time'));

        // Simulate Tab 2: User clicks different OAuth link for registration
        $tab2OAuthUrl = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => '<EMAIL>',
        ]);

        // Simulate new OAuth request (Tab 2) - this should override Tab 1
        $this->withSession([
            'url' => ['previous' => $tab2OAuthUrl],
        ]);

        // Clear and set new OAuth session state
        Session::put('redirect_uri', $tab2OAuthUrl);
        Session::put('is_first_time', true);

        // Tab 2 request should redirect to registration with email
        $response2 = $this->get(route('login'));
        $response2->assertRedirect(route('register', ['email' => '<EMAIL>']));
    }

    /**
     * Test cross-tab scenario: User has stale session data from previous OAuth flow
     */
    public function test_cross_tab_stale_session_cleanup(): void
    {
        $this->assertDatabaseIsolation();

        // Simulate stale session data from previous OAuth flow
        Session::put('redirect_uri', '/old/oauth/url');
        Session::put('is_first_time', false); // Already processed
        Session::put('prefill_email', '<EMAIL>');
        Session::put('google_sso_email', '<EMAIL>');
        Session::put('oauth_params_backup', ['stale' => 'data']);

        // User opens new OAuth link in different tab
        $newOAuthUrl = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => '<EMAIL>',
        ]);

        // Simulate new OAuth request
        $this->withSession([
            'url' => ['previous' => $newOAuthUrl],
        ]);

        // Set new OAuth session state
        Session::put('redirect_uri', $newOAuthUrl);
        Session::put('is_first_time', true);

        // New request should work with fresh data
        $response = $this->get(route('login'));

        // The login page should load (not redirect) because is_first_time processing
        // happens during the login page load, not as a redirect
        $response->assertStatus(200);
        $response->assertViewIs('auth.login');

        // Verify is_first_time was processed
        $this->assertFalse(Session::get('is_first_time'));
    }

    /**
     * Test cross-tab scenario: User authenticates in one tab while OAuth request is pending in another
     */
    public function test_cross_tab_authentication_with_pending_oauth(): void
    {
        $this->assertDatabaseIsolation();

        // Create a verified user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'verified' => 1,
        ]);

        // Simulate Tab 1: OAuth request pending
        $oauthUrl = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
        ]);

        Session::put('redirect_uri', $oauthUrl);
        Session::put('authRequest', [
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'scope' => '',
            'response_type' => 'code',
        ]);

        // Simulate Tab 2: User logs in
        $loginResponse = $this->post(route('login'), [
            'email' => $user->email,
            'password' => 'password123',
        ]);

        // Should redirect to OAuth authorization after login
        $expectedUrl = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'scope' => '',
            'response_type' => 'code',
        ]);

        $loginResponse->assertRedirect($expectedUrl);
    }

    /**
     * Test cross-tab scenario: Multiple Gmail registration attempts
     */
    public function test_cross_tab_multiple_gmail_registration_attempts(): void
    {
        $this->assertDatabaseIsolation();

        // Simulate Tab 1: Gmail registration attempt
        $tab1OAuthUrl = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => '<EMAIL>',
        ]);

        Session::put('redirect_uri', $tab1OAuthUrl);
        Session::put('is_first_time', true);

        // Tab 1 should redirect to Google SSO
        $response1 = $this->get(route('login'));
        $response1->assertRedirect(route('passport.socialite.redirect', 'google'));

        // Verify Google SSO email was set
        $this->assertEquals('<EMAIL>', Session::get('google_sso_email'));

        // Simulate Tab 2: Different Gmail registration attempt
        $tab2OAuthUrl = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => '<EMAIL>',
        ]);

        // Clear previous session and set new OAuth request
        Session::put('redirect_uri', $tab2OAuthUrl);
        Session::put('is_first_time', true);

        // Tab 2 should also redirect to Google SSO with new email
        $response2 = $this->get(route('login'));
        $response2->assertRedirect(route('passport.socialite.redirect', 'google'));

        // Verify Google SSO email was updated
        $this->assertEquals('<EMAIL>', Session::get('google_sso_email'));
    }

    /**
     * Test cross-tab scenario: Session persistence after session regeneration
     */
    public function test_cross_tab_session_persistence_after_regeneration(): void
    {
        $this->assertDatabaseIsolation();

        // Create a verified user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'verified' => 1,
        ]);

        // Simulate OAuth request with is_register parameter
        $oauthUrl = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => '<EMAIL>',
        ]);

        Session::put('redirect_uri', $oauthUrl);
        Session::put('is_first_time', true);

        // Simulate OAuth parameters being persisted before session regeneration
        Session::put('oauth_params_backup', [
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => '<EMAIL>',
        ]);

        // Simulate session regeneration (like what happens during login)
        Session::regenerate();

        // After session regeneration, the OAuth flow should still work
        $response = $this->get(route('login'));
        $response->assertRedirect(route('register', ['email' => '<EMAIL>']));

        // Verify persisted parameters were cleared after processing
        $this->assertNull(Session::get('oauth_params_backup'));
    }

    /**
     * Test cross-tab scenario: User existence checking with concurrent requests
     */
    public function test_cross_tab_user_existence_checking(): void
    {
        $this->assertDatabaseIsolation();

        // Create an existing user
        $existingUser = User::factory()->create(['email' => '<EMAIL>']);

        // Simulate Tab 1: Registration attempt with existing email
        $tab1OAuthUrl = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => $existingUser->email,
        ]);

        Session::put('redirect_uri', $tab1OAuthUrl);
        Session::put('is_first_time', true);

        // Tab 1 should show login form with pre-filled email (user exists)
        $response1 = $this->get(route('login'));
        $response1->assertStatus(200);
        $response1->assertViewIs('auth.login');
        $response1->assertViewHas('prefill_email', $existingUser->email);

        // Simulate Tab 2: Registration attempt with new email
        $tab2OAuthUrl = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => '<EMAIL>',
        ]);

        Session::put('redirect_uri', $tab2OAuthUrl);
        Session::put('is_first_time', true);

        // Tab 2 should redirect to registration (user doesn't exist)
        $response2 = $this->get(route('login'));
        $response2->assertRedirect(route('register', ['email' => '<EMAIL>']));
    }

    /**
     * Test cross-tab scenario: Direct access interference prevention
     */
    public function test_cross_tab_direct_access_interference_prevention(): void
    {
        $this->assertDatabaseIsolation();

        // Simulate stale OAuth session data
        Session::put('redirect_uri', '/stale/oauth/url');
        Session::put('is_first_time', false);
        Session::put('prefill_email', '<EMAIL>');

        // User directly accesses login page (no OAuth flow)
        $response = $this->get(route('login'));

        // Should show clean login page without interference
        $response->assertStatus(200);
        $response->assertViewIs('auth.login');
        $response->assertViewMissing('prefill_email');

        // Note: Session data cleanup happens during OAuth processing, not direct access
        // Direct access to login page doesn't trigger OAuth session cleanup
        $this->assertTrue(true); // Test passes if we reach here without errors
    }
}
