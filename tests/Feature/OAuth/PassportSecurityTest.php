<?php

namespace Tests\Feature\OAuth;

use App\Models\Passport\Client;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PassportSecurityTest extends TestCase
{
    use RefreshDatabase;



    protected Client $client;
    protected Client $confidentialClient;
    protected Client $publicClient;

    #[\Override]
    protected function setUp(): void
    {
        parent::setUp();

        // Create a standard OAuth client
        $this->client = Client::create([
            'id' => 'test-security-client',
            'name' => 'Test Security Client',
            'secret' => 'test-secret-key',
            'redirect' => 'http://localhost/test/callback',
            'personal_access_client' => false,
            'password_client' => false,
            'revoked' => false,
        ]);

        // Create a confidential client (with secret)
        $this->confidentialClient = Client::create([
            'id' => 'confidential-client',
            'name' => 'Confidential Client',
            'secret' => 'confidential-secret',
            'redirect' => 'http://localhost/confidential/callback',
            'personal_access_client' => false,
            'password_client' => false,
            'revoked' => false,
        ]);

        // Create a public client (no secret for PKCE)
        $this->publicClient = Client::create([
            'id' => 'public-client',
            'name' => 'Public Client',
            'secret' => null, // Public client has no secret
            'redirect' => 'http://localhost/public/callback',
            'personal_access_client' => false,
            'password_client' => false,
            'revoked' => false,
        ]);
    }

    /**
     * Test client secret verification for confidential clients
     */
    public function test_client_secret_verification_for_confidential_client(): void
    {
        $this->assertDatabaseIsolation();

        // Create and authenticate a verified user
        $user = User::factory()->create([
            'verified' => 1,
            'email_verified_at' => now(),
        ]);
        $this->actingAs($user);

        // Test OAuth authorization with confidential client
        $oauthParams = [
            'client_id' => $this->confidentialClient->id,
            'redirect_uri' => 'http://localhost/confidential/callback',
            'response_type' => 'code',
            'scope' => '',
            'state' => 'test-state',
        ];

        $response = $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // Should redirect to callback with authorization code (auto-approved)
        $response->assertStatus(302);
        $location = $response->headers->get('Location');
        $this->assertStringContainsString('http://localhost/confidential/callback', $location);
        $this->assertStringContainsString('code=', $location);
        $this->assertStringContainsString('state=test-state', $location);
    }

    /**
     * Test invalid client ID rejection
     */
    public function test_invalid_client_id_rejection(): void
    {
        $this->assertDatabaseIsolation();

        // Create and authenticate a user
        $user = User::factory()->create(['verified' => 1]);
        $this->actingAs($user);

        // Test OAuth authorization with invalid client ID
        $oauthParams = [
            'client_id' => 'invalid-client-id',
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
        ];

        $response = $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // Should return 401 Unauthorized for invalid client
        $response->assertStatus(401);
    }

    /**
     * Test redirect URI validation
     */
    public function test_redirect_uri_validation(): void
    {
        $this->assertDatabaseIsolation();

        // Create and authenticate a user
        $user = User::factory()->create(['verified' => 1]);
        $this->actingAs($user);

        // Test OAuth authorization with invalid redirect URI
        $oauthParams = [
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://malicious-site.com/callback',
            'response_type' => 'code',
            'scope' => '',
        ];

        $response = $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // Should return 401 Unauthorized for invalid redirect URI
        $response->assertStatus(401);
    }

    /**
     * Test valid redirect URI acceptance
     */
    public function test_valid_redirect_uri_acceptance(): void
    {
        $this->assertDatabaseIsolation();

        // Create and authenticate a user
        $user = User::factory()->create(['verified' => 1]);
        $this->actingAs($user);

        // Test OAuth authorization with valid redirect URI
        $oauthParams = [
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
        ];

        $response = $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // Should redirect to callback with authorization code (auto-approved)
        $response->assertStatus(302);
        $location = $response->headers->get('Location');
        $this->assertStringContainsString('http://localhost/test/callback', $location);
        $this->assertStringContainsString('code=', $location);
    }

    /**
     * Test authorization code generation and validation
     */
    public function test_authorization_code_generation(): void
    {
        $this->assertDatabaseIsolation();

        // Create and authenticate a user
        $user = User::factory()->create(['verified' => 1]);
        $this->actingAs($user);

        // First, get the authorization page
        $oauthParams = [
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'state' => 'test-state-123',
        ];

        $authResponse = $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // Should redirect to callback with authorization code (auto-approved)
        $authResponse->assertStatus(302);
        $location = $authResponse->headers->get('Location');
        $this->assertStringContainsString('http://localhost/test/callback', $location);
        $this->assertStringContainsString('code=', $location);
        $this->assertStringContainsString('state=test-state-123', $location);

        // Extract the authorization code
        $query = parse_url($location, PHP_URL_QUERY);
        parse_str($query, $params);
        $this->assertArrayHasKey('code', $params);
        $this->assertArrayHasKey('state', $params);
        $this->assertEquals('test-state-123', $params['state']);
    }

    /**
     * Test token exchange with valid authorization code
     */
    public function test_token_exchange_with_valid_code(): void
    {
        $this->assertDatabaseIsolation();

        // Create and authenticate a user
        $user = User::factory()->create(['verified' => 1]);
        $this->actingAs($user);

        // Get authorization code first
        $oauthParams = [
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'state' => 'test-state',
        ];

        $authResponse = $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // Extract authorization code from auto-approved response
        $authResponse->assertStatus(302);
        $location = $authResponse->headers->get('Location');
        $query = parse_url($location, PHP_URL_QUERY);
        parse_str($query, $params);
        $authCode = $params['code'];

        // Exchange code for token
        $tokenResponse = $this->post('/oauth/token', [
            'grant_type' => 'authorization_code',
            'client_id' => $this->client->id,
            'client_secret' => $this->client->secret,
            'redirect_uri' => 'http://localhost/test/callback',
            'code' => $authCode,
        ]);

        $tokenResponse->assertStatus(200);
        $tokenData = $tokenResponse->json();
        $this->assertArrayHasKey('access_token', $tokenData);
        $this->assertArrayHasKey('token_type', $tokenData);
        $this->assertEquals('Bearer', $tokenData['token_type']);
    }

    /**
     * Test token exchange with invalid client secret
     */
    public function test_token_exchange_with_invalid_client_secret(): void
    {
        $this->assertDatabaseIsolation();

        // Create and authenticate a user
        $user = User::factory()->create(['verified' => 1]);
        $this->actingAs($user);

        // Get authorization code first
        $oauthParams = [
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
        ];

        $authResponse = $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // Extract authorization code from auto-approved response
        $authResponse->assertStatus(302);
        $location = $authResponse->headers->get('Location');
        $query = parse_url($location, PHP_URL_QUERY);
        parse_str($query, $params);
        $authCode = $params['code'];

        // Try to exchange code with wrong client secret
        $tokenResponse = $this->post('/oauth/token', [
            'grant_type' => 'authorization_code',
            'client_id' => $this->client->id,
            'client_secret' => 'wrong-secret',
            'redirect_uri' => 'http://localhost/test/callback',
            'code' => $authCode,
        ]);

        $tokenResponse->assertStatus(401);
        $errorData = $tokenResponse->json();
        $this->assertEquals('invalid_client', $errorData['error']);
    }

    /**
     * Test revoked client rejection
     */
    public function test_revoked_client_rejection(): void
    {
        $this->assertDatabaseIsolation();

        // Revoke the client
        $this->client->update(['revoked' => true]);

        // Create and authenticate a user
        $user = User::factory()->create(['verified' => 1]);
        $this->actingAs($user);

        // Test OAuth authorization with revoked client
        $oauthParams = [
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
        ];

        $response = $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // Should return 401 Unauthorized for revoked client
        $response->assertStatus(401);
    }

    /**
     * Test PKCE code challenge validation (if PKCE is implemented)
     */
    public function test_pkce_code_challenge_validation(): void
    {
        $this->assertDatabaseIsolation();

        // Create and authenticate a user
        $user = User::factory()->create(['verified' => 1]);
        $this->actingAs($user);

        // Generate PKCE parameters
        $codeVerifier = $this->generateCodeVerifier();
        $codeChallenge = $this->generateCodeChallenge($codeVerifier);

        // Test OAuth authorization with PKCE parameters
        $oauthParams = [
            'client_id' => $this->publicClient->id,
            'redirect_uri' => 'http://localhost/public/callback',
            'response_type' => 'code',
            'scope' => '',
            'code_challenge' => $codeChallenge,
            'code_challenge_method' => 'S256',
            'state' => 'pkce-test-state',
        ];

        $response = $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // Should redirect to callback with authorization code (PKCE parameters accepted)
        $response->assertStatus(302);
        $location = $response->headers->get('Location');
        $this->assertStringContainsString('http://localhost/public/callback', $location);
        $this->assertStringContainsString('code=', $location);
        $this->assertStringContainsString('state=pkce-test-state', $location);
    }

    /**
     * Test session security and state parameter validation
     */
    public function test_state_parameter_validation(): void
    {
        $this->assertDatabaseIsolation();

        // Create and authenticate a user
        $user = User::factory()->create(['verified' => 1]);
        $this->actingAs($user);

        $originalState = 'secure-random-state-' . uniqid();

        // Get authorization page with state parameter
        $oauthParams = [
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'state' => $originalState,
        ];

        $authResponse = $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // Should redirect with same state parameter (auto-approved)
        $authResponse->assertStatus(302);
        $location = $authResponse->headers->get('Location');
        $this->assertStringContainsString("state={$originalState}", $location);
        $this->assertStringContainsString('code=', $location);
    }

    /**
     * Test authorization denial handling
     */
    public function test_authorization_denial_handling(): void
    {
        $this->assertDatabaseIsolation();

        // Create and authenticate a user
        $user = User::factory()->create(['verified' => 1]);
        $this->actingAs($user);

        $state = 'denial-test-state';

        // Get authorization page
        $oauthParams = [
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'state' => $state,
        ];

        $authResponse = $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // Should auto-approve and redirect with authorization code
        $authResponse->assertStatus(302);
        $location = $authResponse->headers->get('Location');
        $this->assertStringContainsString('code=', $location);
        $this->assertStringContainsString("state={$state}", $location);

        // Note: Authorization denial testing would require manual approval flow
        // which is not available in this auto-approval configuration
    }

    /**
     * Test multiple concurrent authorization requests
     */
    public function test_multiple_concurrent_authorization_requests(): void
    {
        $this->assertDatabaseIsolation();

        // Create and authenticate a user
        $user = User::factory()->create(['verified' => 1]);
        $this->actingAs($user);

        // First authorization request
        $oauthParams1 = [
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'state' => 'request-1',
        ];

        $response1 = $this->get('/oauth/authorize?' . http_build_query($oauthParams1));
        $response1->assertStatus(302);
        $location1 = $response1->headers->get('Location');
        $this->assertStringContainsString('code=', $location1);
        $this->assertStringContainsString('state=request-1', $location1);

        // Second authorization request (different client)
        $oauthParams2 = [
            'client_id' => $this->confidentialClient->id,
            'redirect_uri' => 'http://localhost/confidential/callback',
            'response_type' => 'code',
            'scope' => '',
            'state' => 'request-2',
        ];

        $response2 = $this->get('/oauth/authorize?' . http_build_query($oauthParams2));
        $response2->assertStatus(302);
        $location2 = $response2->headers->get('Location');
        $this->assertStringContainsString('code=', $location2);
        $this->assertStringContainsString('state=request-2', $location2);

        // Both requests should be handled independently with different codes
        $this->assertNotEquals($location1, $location2);
    }

    /**
     * Helper method to generate PKCE code verifier
     */
    private function generateCodeVerifier(): string
    {
        return rtrim(strtr(base64_encode(random_bytes(32)), '+/', '-_'), '=');
    }

    /**
     * Helper method to generate PKCE code challenge
     */
    private function generateCodeChallenge(string $codeVerifier): string
    {
        return rtrim(strtr(base64_encode(hash('sha256', $codeVerifier, true)), '+/', '-_'), '=');
    }
}
