<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Rules\ValidEmailProvider;
use App\Traits\Passport\PassportRedirectTrait;
use Illuminate\Foundation\Auth\RegistersUsers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class RegisterController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */

    use RegistersUsers;
    use PassportRedirectTrait;
    use PassportRedirectTrait;

    /**
     * Where to redirect users after registration.
     *
     * @var string
     */
    protected $redirectTo = '/email/verify';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

    /**
     * Show the application registration form.
     * Enhanced to handle email pre-filling for new registration scenarios.
     *
     * @return \Illuminate\View\View
     */
    public function showRegistrationForm()
    {
        $this->setDefaultRedirection();

        // Check for pre-filled email from registration flows
        $prefillEmail = request('email') ?? session('google_sso_email');

        // Clean up session data after retrieving
        session()->forget('google_sso_email');

        return view('auth.register', ['prefill_email' => $prefillEmail]);
    }

    public function registered(\Illuminate\Http\Request $request, $user)
    {
        Session::put('verify_user_id', auth()->id());

        $this->guard()->logout();

        // Return explicit redirect to email verification
        return redirect($this->redirectTo);
    }

    /**
     * Get a validator for an incoming registration request.
     *
     * @param  array  $data
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validator(array $data)
    {
        return Validator::make($data, [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users', new ValidEmailProvider()],
            'password' => ['required', 'string', 'min:8'],
        ]);
    }

    /**
     * Create a new user instance after a valid registration.
     *
     * @param  array  $data
     * @return \App\Models\User
     */
    protected function create(array $data)
    {
        $user = User::create([
            'name' => $data['name'],
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
        ]);

        createInitSubscription($user);

        return $user;
    }
}
